"""
Main game class that orchestrates all game components.
"""

import pygame
from pygame.locals import *
from OpenGL.GL import *
from OpenGL.GLU import *
import sys

from .renderer import Renderer
from .maze import Maze
from .player import Player
from .game_state import GameState

class MazeGame:
    """Main game class that manages the game loop and coordinates all components."""
    
    def __init__(self, width=1024, height=768):
        """Initialize the maze game."""
        self.width = width
        self.height = height
        self.clock = pygame.time.Clock()
        self.running = True
        
        # Initialize display
        pygame.display.set_mode((self.width, self.height), DOUBLEBUF | OPENGL)
        pygame.display.set_caption("3D Maze Game")
        
        # Hide mouse cursor and capture mouse
        pygame.mouse.set_visible(False)
        pygame.event.set_grab(True)
        
        # Initialize game components
        self.maze = Maze(31, 31)  # Create a 31x31 tactical arena
        self.player = Player(self.maze.start_pos)
        self.renderer = Renderer(self.width, self.height)
        self.game_state = GameState()

        # Start the game timer
        self.game_state.start_game()
        
        # Set up OpenGL
        self._setup_opengl()
    
    def _setup_opengl(self):
        """Configure enhanced OpenGL settings."""
        # Enable depth testing and face culling for performance
        glEnable(GL_DEPTH_TEST)
        glEnable(GL_CULL_FACE)
        glCullFace(GL_BACK)

        # Enhanced lighting setup
        glEnable(GL_LIGHTING)
        glEnable(GL_LIGHT0)
        glEnable(GL_LIGHT1)  # Add second light
        glEnable(GL_COLOR_MATERIAL)
        glColorMaterial(GL_FRONT_AND_BACK, GL_AMBIENT_AND_DIFFUSE)

        # Main light (overhead)
        glLightfv(GL_LIGHT0, GL_POSITION, [10, 15, 10, 1])
        glLightfv(GL_LIGHT0, GL_AMBIENT, [0.2, 0.2, 0.3, 1])
        glLightfv(GL_LIGHT0, GL_DIFFUSE, [0.6, 0.6, 0.8, 1])
        glLightfv(GL_LIGHT0, GL_SPECULAR, [0.3, 0.3, 0.4, 1])

        # Secondary light (warmer, lower)
        glLightfv(GL_LIGHT1, GL_POSITION, [5, 3, 5, 1])
        glLightfv(GL_LIGHT1, GL_AMBIENT, [0.1, 0.05, 0.0, 1])
        glLightfv(GL_LIGHT1, GL_DIFFUSE, [0.4, 0.3, 0.2, 1])
        glLightfv(GL_LIGHT1, GL_SPECULAR, [0.2, 0.15, 0.1, 1])

        # Enable smooth shading
        glShadeModel(GL_SMOOTH)

        # Set background color (darker, more atmospheric)
        glClearColor(0.05, 0.05, 0.15, 1.0)
    
    def handle_events(self):
        """Handle pygame events."""
        for event in pygame.event.get():
            if event.type == QUIT:
                self.running = False
            elif event.type == KEYDOWN:
                if event.key == K_ESCAPE:
                    self.running = False
            elif event.type == MOUSEMOTION:
                # Handle mouse look
                self.player.handle_mouse_look(event.rel[0], event.rel[1])
    
    def update(self, dt):
        """Update game state."""
        # Handle keyboard input for movement
        keys = pygame.key.get_pressed()
        self.player.update(keys, dt, self.maze)

        # TODO: Add combat mechanics, enemy AI, weapon systems
        # For now, no win condition - this is a combat arena
    
    def render(self):
        """Render the game with dynamic lighting."""
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT)

        # Update dynamic lighting based on player position
        px, py, pz = self.player.position
        glLightfv(GL_LIGHT1, GL_POSITION, [px, py + 1, pz, 1])  # Light follows player

        # Set up camera
        self.renderer.setup_camera(self.player)

        # Render maze
        self.renderer.render_maze(self.maze)

        # Render UI
        self.renderer.render_ui(self.game_state)

        pygame.display.flip()
    
    def run(self):
        """Main game loop for tactical arena."""
        while self.running:
            dt = self.clock.tick(60) / 1000.0  # Delta time in seconds

            self.handle_events()
            self.update(dt)
            self.render()

            # TODO: Add combat win/lose conditions
            # For now, runs indefinitely as a tactical arena
