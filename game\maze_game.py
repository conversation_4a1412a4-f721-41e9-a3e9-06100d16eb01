"""
Main game class that orchestrates all game components.
"""

import pygame
from pygame.locals import *
from OpenGL.GL import *
from OpenGL.GLU import *
import sys

from .renderer import Renderer
from .maze import Maze
from .player import Player
from .game_state import GameState

class MazeGame:
    """Main game class that manages the game loop and coordinates all components."""
    
    def __init__(self, width=1024, height=768):
        """Initialize the maze game."""
        self.width = width
        self.height = height
        self.clock = pygame.time.Clock()
        self.running = True
        
        # Initialize display
        pygame.display.set_mode((self.width, self.height), DOUBLEBUF | OPENGL)
        pygame.display.set_caption("3D Maze Game")
        
        # Hide mouse cursor and capture mouse
        pygame.mouse.set_visible(False)
        pygame.event.set_grab(True)
        
        # Initialize game components
        self.maze = Maze(21, 21)  # Create a 21x21 maze
        self.player = Player(self.maze.start_pos)
        self.renderer = Renderer(self.width, self.height)
        self.game_state = GameState()

        # Start the game timer
        self.game_state.start_game()
        
        # Set up OpenGL
        self._setup_opengl()
    
    def _setup_opengl(self):
        """Configure OpenGL settings."""
        glEnable(GL_DEPTH_TEST)
        glEnable(GL_LIGHTING)
        glEnable(GL_LIGHT0)
        glEnable(GL_COLOR_MATERIAL)
        
        # Set up lighting
        glLightfv(GL_LIGHT0, GL_POSITION, [0, 10, 0, 1])
        glLightfv(GL_LIGHT0, GL_AMBIENT, [0.3, 0.3, 0.3, 1])
        glLightfv(GL_LIGHT0, GL_DIFFUSE, [0.8, 0.8, 0.8, 1])
        
        # Set background color
        glClearColor(0.1, 0.1, 0.2, 1.0)
    
    def handle_events(self):
        """Handle pygame events."""
        for event in pygame.event.get():
            if event.type == QUIT:
                self.running = False
            elif event.type == KEYDOWN:
                if event.key == K_ESCAPE:
                    self.running = False
            elif event.type == MOUSEMOTION:
                # Handle mouse look
                self.player.handle_mouse_look(event.rel[0], event.rel[1])
    
    def update(self, dt):
        """Update game state."""
        # Handle keyboard input for movement
        keys = pygame.key.get_pressed()
        self.player.update(keys, dt, self.maze)
        
        # Check win condition
        if self.maze.is_at_exit(self.player.position):
            self.game_state.set_won()
    
    def render(self):
        """Render the game."""
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT)
        
        # Set up camera
        self.renderer.setup_camera(self.player)
        
        # Render maze
        self.renderer.render_maze(self.maze)
        
        # Render UI
        self.renderer.render_ui(self.game_state)
        
        pygame.display.flip()
    
    def run(self):
        """Main game loop."""
        while self.running:
            dt = self.clock.tick(60) / 1000.0  # Delta time in seconds
            
            self.handle_events()
            
            if not self.game_state.is_won():
                self.update(dt)
            
            self.render()
            
            # Check if game is won
            if self.game_state.is_won():
                print("Congratulations! You reached the exit!")
                pygame.time.wait(2000)  # Wait 2 seconds before closing
                self.running = False
