#!/usr/bin/env python3
"""
3D First-Person Maze Game
A simple 3D maze game with first-person navigation using Pygame and OpenGL.
"""

import pygame
import sys
from game.maze_game import MazeGame

def main():
    """Main entry point for the maze game."""
    try:
        # Initialize pygame
        pygame.init()
        
        # Create and run the game
        game = MazeGame()
        game.run()
        
    except Exception as e:
        print(f"Error running game: {e}")
        return 1
    
    finally:
        pygame.quit()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
