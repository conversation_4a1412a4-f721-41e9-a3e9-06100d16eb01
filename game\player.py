"""
Player class handling movement, rotation, and collision detection.
Implements first-person controls with WASD movement and mouse look.
"""

import math
import pygame
from pygame.locals import *

class Player:
    """Represents the player with position, rotation, and movement capabilities."""
    
    def __init__(self, start_position):
        """
        Initialize player at starting position.
        
        Args:
            start_position: Tuple (x, y, z) for initial position
        """
        self.position = list(start_position)  # [x, y, z]
        self.yaw = 0.0      # Horizontal rotation (degrees)
        self.pitch = 0.0    # Vertical rotation (degrees)
        
        # Movement settings
        self.move_speed = 5.0      # Units per second
        self.mouse_sensitivity = 0.1
        self.max_pitch = 89.0      # Prevent camera flip
        
        # Player dimensions for collision
        self.radius = 0.3  # Collision radius
        self.height = 1.8  # Player height
    
    def handle_mouse_look(self, dx, dy):
        """
        Handle mouse movement for camera rotation.

        Args:
            dx: Mouse movement in X direction
            dy: Mouse movement in Y direction
        """
        # Update yaw (horizontal rotation) - invert dx for natural feel
        self.yaw -= dx * self.mouse_sensitivity

        # Update pitch (vertical rotation) with limits
        self.pitch -= dy * self.mouse_sensitivity
        self.pitch = max(-self.max_pitch, min(self.max_pitch, self.pitch))

        # Keep yaw in 0-360 range
        self.yaw = self.yaw % 360
    
    def update(self, keys, dt, maze):
        """
        Update player position based on input and handle collision.
        
        Args:
            keys: Pygame key state
            dt: Delta time in seconds
            maze: Maze object for collision detection
        """
        # Calculate movement vectors based on current rotation
        yaw_rad = math.radians(self.yaw)

        # Forward/backward vector (in XZ plane)
        forward_x = math.sin(yaw_rad)
        forward_z = math.cos(yaw_rad)

        # Right vector (perpendicular to forward)
        right_x = -math.cos(yaw_rad)
        right_z = math.sin(yaw_rad)
        
        # Calculate desired movement
        move_x = 0
        move_z = 0
        
        # WASD movement
        if keys[K_w] or keys[K_UP]:
            move_x += forward_x
            move_z += forward_z
        if keys[K_s] or keys[K_DOWN]:
            move_x -= forward_x
            move_z -= forward_z
        if keys[K_a] or keys[K_LEFT]:
            move_x -= right_x
            move_z -= right_z
        if keys[K_d] or keys[K_RIGHT]:
            move_x += right_x
            move_z += right_z
        
        # Normalize movement vector if moving diagonally
        if move_x != 0 or move_z != 0:
            length = math.sqrt(move_x * move_x + move_z * move_z)
            move_x /= length
            move_z /= length
        
        # Apply movement speed and delta time
        move_x *= self.move_speed * dt
        move_z *= self.move_speed * dt
        
        # Handle collision and update position
        self._move_with_collision(move_x, move_z, maze)
    
    def _move_with_collision(self, dx, dz, maze):
        """
        Move player with collision detection against maze walls.
        
        Args:
            dx: Desired movement in X direction
            dz: Desired movement in Z direction
            maze: Maze object for collision checking
        """
        # Current position
        x, y, z = self.position
        
        # Try moving in X direction first
        new_x = x + dx
        if not self._check_collision(new_x, z, maze):
            self.position[0] = new_x
        
        # Try moving in Z direction
        new_z = z + dz
        if not self._check_collision(self.position[0], new_z, maze):
            self.position[2] = new_z
    
    def _check_collision(self, x, z, maze):
        """
        Check if player would collide with walls at given position.
        
        Args:
            x: X position to check
            z: Z position to check
            maze: Maze object
            
        Returns:
            True if collision detected, False otherwise
        """
        # Check collision at multiple points around player's radius
        check_points = [
            (x + self.radius, z),           # Right
            (x - self.radius, z),           # Left
            (x, z + self.radius),           # Forward
            (x, z - self.radius),           # Backward
            (x + self.radius, z + self.radius),   # Front-right
            (x - self.radius, z + self.radius),   # Front-left
            (x + self.radius, z - self.radius),   # Back-right
            (x - self.radius, z - self.radius),   # Back-left
        ]
        
        # Check if any point collides with a wall
        for check_x, check_z in check_points:
            if maze.is_wall(check_x, check_z):
                return True
        
        return False
    
    def get_forward_vector(self):
        """
        Get the forward direction vector.

        Returns:
            Tuple (x, z) representing forward direction
        """
        yaw_rad = math.radians(self.yaw)
        return (math.sin(yaw_rad), math.cos(yaw_rad))

    def get_right_vector(self):
        """
        Get the right direction vector.

        Returns:
            Tuple (x, z) representing right direction
        """
        yaw_rad = math.radians(self.yaw)
        return (-math.cos(yaw_rad), math.sin(yaw_rad))
