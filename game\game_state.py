"""
Game state management for tracking game progress and conditions.
"""

class GameState:
    """Manages the current state of the game."""
    
    def __init__(self):
        """Initialize game state."""
        self.won = False
        self.start_time = None
        self.end_time = None
        self.score = 0
    
    def set_won(self):
        """Mark the game as won."""
        if not self.won:
            self.won = True
            import time
            self.end_time = time.time()
    
    def is_won(self):
        """
        Check if the game has been won.
        
        Returns:
            True if game is won, False otherwise
        """
        return self.won
    
    def reset(self):
        """Reset game state for a new game."""
        self.won = False
        self.start_time = None
        self.end_time = None
        self.score = 0
    
    def get_play_time(self):
        """
        Get the current play time in seconds.
        
        Returns:
            Play time in seconds, or None if game hasn't started
        """
        if self.start_time is None:
            return None
        
        import time
        end = self.end_time if self.end_time else time.time()
        return end - self.start_time
    
    def start_game(self):
        """Mark the start of the game."""
        if self.start_time is None:
            import time
            self.start_time = time.time()
