# 3D First-Person Maze Game

A simple 3D first-person maze game built with Python, Pygame, and OpenGL. Navigate through a procedurally generated maze to reach the exit!

## Features

- **3D First-Person Perspective**: Immersive first-person navigation through the maze
- **Procedural Maze Generation**: Each game features a unique maze layout using recursive backtracking algorithm
- **Smooth Controls**: WASD movement with mouse look functionality
- **Collision Detection**: Realistic wall collision prevents walking through obstacles
- **Visual Feedback**: Clear start (green) and exit (red) markers with pulsing effects
- **Checkered Floor Pattern**: Enhanced visual depth with alternating floor tiles
- **Dynamic Lighting**: Basic OpenGL lighting for improved 3D appearance

## Requirements

- Python 3.7 or higher
- OpenGL-compatible graphics card
- Mouse and keyboard

## Installation

1. **Clone or download the project files**

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

   The required packages are:
   - pygame (2.5.2)
   - PyOpenGL (3.1.7)
   - PyOpenGL-accelerate (3.1.7)
   - numpy (1.24.3)
   - Pillow (10.0.0)

3. **Run the game**:
   ```bash
   python main.py
   ```

## Controls

- **Movement**: WASD keys or Arrow keys
  - W/Up Arrow: Move forward
  - S/Down Arrow: Move backward
  - A/Left Arrow: Strafe left
  - D/Right Arrow: Strafe right

- **Camera**: Mouse movement
  - Move mouse to look around
  - Vertical movement is limited to prevent camera flipping

- **Exit Game**: ESC key or close window

## Game Objective

Navigate from the **green start marker** to the **red exit marker** to win the game. The maze is procedurally generated, so each playthrough offers a unique challenge.

## Project Structure

```
├── main.py                 # Main entry point
├── requirements.txt        # Python dependencies
├── README.md              # This file
└── game/                  # Game package
    ├── __init__.py        # Package initialization
    ├── maze_game.py       # Main game class and game loop
    ├── maze.py            # Maze generation and management
    ├── player.py          # Player movement and controls
    ├── renderer.py        # 3D rendering engine
    └── game_state.py      # Game state management
```

## Technical Details

### Maze Generation
The game uses a **recursive backtracking algorithm** to generate mazes:
1. Start with a grid filled with walls
2. Carve paths by randomly selecting unvisited neighbors
3. Backtrack when no unvisited neighbors remain
4. Continue until all reachable cells are visited

### 3D Rendering
- **OpenGL**: Hardware-accelerated 3D graphics
- **Perspective Projection**: Realistic 3D depth perception
- **Basic Lighting**: Ambient and diffuse lighting for visual depth
- **Collision Detection**: Multi-point collision checking around player radius

### Performance
- **60 FPS Target**: Smooth gameplay with consistent frame rate
- **Efficient Rendering**: Optimized OpenGL calls for maze rendering
- **Collision Optimization**: Fast wall detection using grid-based lookup

## Troubleshooting

### Common Issues

1. **ImportError: No module named 'OpenGL'**
   - Install PyOpenGL: `pip install PyOpenGL PyOpenGL-accelerate`

2. **Game window is black**
   - Ensure your graphics drivers support OpenGL
   - Try updating your graphics drivers

3. **Mouse sensitivity too high/low**
   - Modify `mouse_sensitivity` in `game/player.py` (line 23)

4. **Performance issues**
   - Reduce maze size in `game/maze_game.py` (line 35)
   - Close other graphics-intensive applications

### System Requirements
- **Minimum**: OpenGL 2.1 support
- **Recommended**: Dedicated graphics card with OpenGL 3.0+
- **RAM**: 512MB minimum
- **CPU**: Any modern processor

## Customization

### Maze Size
Edit `game/maze_game.py`, line 35:
```python
self.maze = Maze(21, 21)  # Change dimensions (must be odd numbers)
```

### Movement Speed
Edit `game/player.py`, line 22:
```python
self.move_speed = 5.0  # Increase for faster movement
```

### Mouse Sensitivity
Edit `game/player.py`, line 23:
```python
self.mouse_sensitivity = 0.1  # Adjust sensitivity
```

## License

This project is open source and available under the MIT License.

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve the game!
