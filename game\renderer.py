"""
3D rendering engine for the maze game using OpenGL.
Handles camera setup, maze rendering, and basic lighting.
"""

import math
from OpenGL.GL import *
from OpenGL.GLU import *
import pygame

class Renderer:
    """Handles all 3D rendering operations for the maze game."""
    
    def __init__(self, width, height):
        """
        Initialize the renderer.
        
        Args:
            width: Screen width
            height: Screen height
        """
        self.width = width
        self.height = height
        
        # Set up perspective projection
        glMatrixMode(GL_PROJECTION)
        glLoadIdentity()
        gluPerspective(60, width / height, 0.1, 100.0)
        glMatrixMode(GL_MODELVIEW)
    
    def setup_camera(self, player):
        """
        Set up the camera based on player position and orientation.
        
        Args:
            player: Player object with position and rotation
        """
        glLoadIdentity()
        
        # Calculate look direction based on player rotation
        yaw_rad = math.radians(player.yaw)
        pitch_rad = math.radians(player.pitch)
        
        # Calculate forward vector
        forward_x = math.cos(pitch_rad) * math.sin(yaw_rad)
        forward_y = math.sin(pitch_rad)
        forward_z = math.cos(pitch_rad) * math.cos(yaw_rad)
        
        # Player position
        px, py, pz = player.position
        
        # Look at point
        look_x = px + forward_x
        look_y = py + forward_y
        look_z = pz + forward_z
        
        # Set up camera
        gluLookAt(px, py, pz,           # Eye position
                  look_x, look_y, look_z,  # Look at point
                  0, 1, 0)              # Up vector
    
    def render_maze(self, maze):
        """
        Render the entire maze.
        
        Args:
            maze: Maze object to render
        """
        # Render floor
        self._render_floor(maze)
        
        # Render walls
        self._render_walls(maze)
        
        # Render start and end markers
        self._render_markers(maze)
    
    def _render_floor(self, maze):
        """Render the maze floor with checkered pattern."""
        glBegin(GL_QUADS)
        for z in range(maze.height):
            for x in range(maze.width):
                if maze.get_cell_type(x, z) != maze.WALL:
                    # Checkered pattern
                    if (x + z) % 2 == 0:
                        glColor3f(0.4, 0.4, 0.4)  # Light gray
                    else:
                        glColor3f(0.2, 0.2, 0.2)  # Dark gray

                    # Render floor tile with normal
                    glNormal3f(0, 1, 0)
                    glVertex3f(x, 0, z)
                    glVertex3f(x + 1, 0, z)
                    glVertex3f(x + 1, 0, z + 1)
                    glVertex3f(x, 0, z + 1)
        glEnd()
    
    def _render_walls(self, maze):
        """Render maze walls with varied colors."""
        for z in range(maze.height):
            for x in range(maze.width):
                if maze.get_cell_type(x, z) == maze.WALL:
                    # Vary wall color slightly based on position
                    color_var = 0.1 * ((x + z) % 3) / 2
                    glColor3f(0.5 + color_var, 0.5 + color_var, 0.7 + color_var)
                    self._render_cube(x, 0, z, 1, 2, 1)
    
    def _render_cube(self, x, y, z, width, height, depth):
        """
        Render a cube at the specified position.
        
        Args:
            x, y, z: Position
            width, height, depth: Dimensions
        """
        glBegin(GL_QUADS)
        
        # Front face
        glNormal3f(0, 0, 1)
        glVertex3f(x, y, z + depth)
        glVertex3f(x + width, y, z + depth)
        glVertex3f(x + width, y + height, z + depth)
        glVertex3f(x, y + height, z + depth)
        
        # Back face
        glNormal3f(0, 0, -1)
        glVertex3f(x, y, z)
        glVertex3f(x, y + height, z)
        glVertex3f(x + width, y + height, z)
        glVertex3f(x + width, y, z)
        
        # Left face
        glNormal3f(-1, 0, 0)
        glVertex3f(x, y, z)
        glVertex3f(x, y, z + depth)
        glVertex3f(x, y + height, z + depth)
        glVertex3f(x, y + height, z)
        
        # Right face
        glNormal3f(1, 0, 0)
        glVertex3f(x + width, y, z)
        glVertex3f(x + width, y + height, z)
        glVertex3f(x + width, y + height, z + depth)
        glVertex3f(x + width, y, z + depth)
        
        # Top face
        glNormal3f(0, 1, 0)
        glVertex3f(x, y + height, z)
        glVertex3f(x, y + height, z + depth)
        glVertex3f(x + width, y + height, z + depth)
        glVertex3f(x + width, y + height, z)
        
        # Bottom face
        glNormal3f(0, -1, 0)
        glVertex3f(x, y, z)
        glVertex3f(x + width, y, z)
        glVertex3f(x + width, y, z + depth)
        glVertex3f(x, y, z + depth)
        
        glEnd()
    
    def _render_markers(self, maze):
        """Render start and end position markers with pulsing effect."""
        import time
        pulse = abs(math.sin(time.time() * 3)) * 0.3 + 0.7  # Pulsing between 0.7 and 1.0

        # Start marker (green with pulse)
        glColor3f(0, pulse, 0)
        start_x, start_y, start_z = maze.start_pos
        self._render_cube(start_x - 0.3, 0.1, start_z - 0.3, 0.6, 0.3, 0.6)

        # End marker (red with pulse)
        glColor3f(pulse, 0, 0)
        end_x, end_y, end_z = maze.end_pos
        self._render_cube(end_x - 0.3, 0.1, end_z - 0.3, 0.6, 0.3, 0.6)
    
    def render_ui(self, game_state):
        """
        Render UI elements.
        
        Args:
            game_state: Current game state
        """
        # Switch to 2D rendering for UI
        glMatrixMode(GL_PROJECTION)
        glPushMatrix()
        glLoadIdentity()
        glOrtho(0, self.width, self.height, 0, -1, 1)
        
        glMatrixMode(GL_MODELVIEW)
        glPushMatrix()
        glLoadIdentity()
        
        # Disable depth testing for UI
        glDisable(GL_DEPTH_TEST)
        
        # Render crosshair
        self._render_crosshair()
        
        # Render win message if game is won
        if game_state.is_won():
            self._render_win_message()
        
        # Re-enable depth testing
        glEnable(GL_DEPTH_TEST)
        
        # Restore matrices
        glPopMatrix()
        glMatrixMode(GL_PROJECTION)
        glPopMatrix()
        glMatrixMode(GL_MODELVIEW)
    
    def _render_crosshair(self):
        """Render a simple crosshair in the center of the screen."""
        glColor3f(1, 1, 1)  # White crosshair
        glLineWidth(2)
        
        center_x = self.width // 2
        center_y = self.height // 2
        size = 10
        
        glBegin(GL_LINES)
        # Horizontal line
        glVertex2f(center_x - size, center_y)
        glVertex2f(center_x + size, center_y)
        # Vertical line
        glVertex2f(center_x, center_y - size)
        glVertex2f(center_x, center_y + size)
        glEnd()
    
    def _render_win_message(self):
        """Render win message overlay."""
        # Semi-transparent overlay
        glColor4f(0, 0, 0, 0.7)
        glEnable(GL_BLEND)
        glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA)
        
        glBegin(GL_QUADS)
        glVertex2f(0, 0)
        glVertex2f(self.width, 0)
        glVertex2f(self.width, self.height)
        glVertex2f(0, self.height)
        glEnd()
        
        glDisable(GL_BLEND)
