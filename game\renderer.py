"""
3D rendering engine for the maze game using OpenGL.
Handles camera setup, maze rendering, and advanced lighting effects.
"""

import math
import time
import numpy as np
from OpenGL.GL import *
from OpenGL.GLU import *
import pygame

class Renderer:
    """Handles all 3D rendering operations for the maze game."""
    
    def __init__(self, width, height):
        """
        Initialize the renderer with enhanced graphics settings.

        Args:
            width: Screen width
            height: Screen height
        """
        self.width = width
        self.height = height

        # Set up perspective projection
        glMatrixMode(GL_PROJECTION)
        glLoadIdentity()
        gluPerspective(75, width / height, 0.1, 50.0)  # Wider FOV, shorter draw distance
        glMatrixMode(GL_MODELVIEW)

        # Initialize texture system
        self._init_textures()

        # Animation time tracking
        self.start_time = time.time()

    def _init_textures(self):
        """Initialize procedural textures for walls, floors, and skybox."""
        # Create wall texture
        self.wall_texture = self._create_brick_texture()

        # Create floor texture
        self.floor_texture = self._create_stone_texture()

        # Create skybox textures (6 faces)
        self.skybox_textures = self._create_skybox_textures()

        # Create particle system for ambient effects
        self.particles = []
        for _ in range(20):
            self.particles.append({
                'x': np.random.uniform(-10, 30),
                'y': np.random.uniform(0.5, 3),
                'z': np.random.uniform(-10, 30),
                'speed': np.random.uniform(0.1, 0.3),
                'phase': np.random.uniform(0, 2 * math.pi)
            })

    def _create_brick_texture(self):
        """Create a procedural brick texture."""
        size = 64
        texture_data = np.zeros((size, size, 3), dtype=np.uint8)

        # Base brick color
        base_color = [120, 80, 60]
        mortar_color = [80, 80, 80]

        for y in range(size):
            for x in range(size):
                # Create brick pattern
                brick_x = x % 16
                brick_y = y % 8
                offset = 8 if (y // 8) % 2 else 0
                brick_x = (brick_x + offset) % 16

                if brick_x < 1 or brick_y < 1:
                    # Mortar
                    texture_data[y, x] = mortar_color
                else:
                    # Brick with some variation
                    variation = np.random.randint(-20, 20)
                    color = [max(0, min(255, c + variation)) for c in base_color]
                    texture_data[y, x] = color

        # Generate OpenGL texture
        texture_id = glGenTextures(1)
        glBindTexture(GL_TEXTURE_2D, texture_id)
        glTexImage2D(GL_TEXTURE_2D, 0, GL_RGB, size, size, 0, GL_RGB, GL_UNSIGNED_BYTE, texture_data)
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR)
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR)
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_REPEAT)
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_REPEAT)

        return texture_id

    def _create_stone_texture(self):
        """Create a procedural stone floor texture."""
        size = 64
        texture_data = np.zeros((size, size, 3), dtype=np.uint8)

        # Base stone colors
        colors = [[100, 100, 110], [90, 90, 100], [110, 110, 120]]

        for y in range(size):
            for x in range(size):
                # Create stone pattern with noise
                noise = np.sin(x * 0.3) * np.cos(y * 0.3) * 20
                base_idx = (x + y) % len(colors)
                color = colors[base_idx]

                # Add noise variation
                final_color = [max(0, min(255, int(c + noise))) for c in color]
                texture_data[y, x] = final_color

        # Generate OpenGL texture
        texture_id = glGenTextures(1)
        glBindTexture(GL_TEXTURE_2D, texture_id)
        glTexImage2D(GL_TEXTURE_2D, 0, GL_RGB, size, size, 0, GL_RGB, GL_UNSIGNED_BYTE, texture_data)
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR)
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR)
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_REPEAT)
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_REPEAT)

        return texture_id

    def _create_skybox_textures(self):
        """Create procedural skybox textures for all 6 faces."""
        size = 256
        textures = {}

        # Create different textures for each face
        faces = ['front', 'back', 'left', 'right', 'top', 'bottom']

        for face in faces:
            texture_data = self._generate_sky_face(size, face)

            # Generate OpenGL texture
            texture_id = glGenTextures(1)
            glBindTexture(GL_TEXTURE_2D, texture_id)
            glTexImage2D(GL_TEXTURE_2D, 0, GL_RGB, size, size, 0, GL_RGB, GL_UNSIGNED_BYTE, texture_data)
            glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR)
            glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR)
            glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE)
            glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE)

            textures[face] = texture_id

        return textures

    def _generate_sky_face(self, size, face):
        """Generate a procedural sky texture for one face of the skybox."""
        texture_data = np.zeros((size, size, 3), dtype=np.uint8)

        for y in range(size):
            for x in range(size):
                # Normalize coordinates to -1 to 1
                nx = (x / size) * 2 - 1
                ny = (y / size) * 2 - 1

                if face == 'top':
                    # Sky with stars and nebula
                    color = self._generate_sky_color(nx, ny, is_top=True)
                elif face == 'bottom':
                    # Ground reflection or void
                    color = self._generate_ground_color(nx, ny)
                else:
                    # Horizon with gradient and clouds
                    color = self._generate_horizon_color(nx, ny, face)

                texture_data[y, x] = color

        return texture_data

    def _generate_sky_color(self, x, y, is_top=False):
        """Generate sky color with stars, nebula, and gradient."""
        # Base night sky gradient
        if is_top:
            # Top of sky - darker space
            base_r, base_g, base_b = 5, 10, 25
        else:
            # Horizon - lighter with atmosphere
            base_r, base_g, base_b = 15, 25, 45

        # Add nebula effect using noise
        nebula_noise = (np.sin(x * 8) * np.cos(y * 6) +
                       np.sin(x * 12) * np.cos(y * 10)) * 0.5
        nebula_intensity = max(0, nebula_noise) * 30

        # Purple/blue nebula colors
        nebula_r = int(nebula_intensity * 0.8)
        nebula_g = int(nebula_intensity * 0.4)
        nebula_b = int(nebula_intensity * 1.2)

        # Add stars
        star_noise = np.sin(x * 50) * np.cos(y * 50) * np.sin(x * 73) * np.cos(y * 67)
        if star_noise > 0.95:  # Only brightest points become stars
            star_brightness = int((star_noise - 0.95) * 5000)
            star_r = star_g = star_b = min(255, star_brightness)
        else:
            star_r = star_g = star_b = 0

        # Combine all effects
        final_r = min(255, base_r + nebula_r + star_r)
        final_g = min(255, base_g + nebula_g + star_g)
        final_b = min(255, base_b + nebula_b + star_b)

        return [final_r, final_g, final_b]

    def _generate_ground_color(self, x, y):
        """Generate ground/void color for bottom face."""
        # Very dark with subtle variation
        base_intensity = 8
        noise = np.sin(x * 3) * np.cos(y * 3) * 3
        intensity = max(0, min(20, base_intensity + noise))

        return [int(intensity * 0.6), int(intensity * 0.4), int(intensity * 0.8)]

    def _generate_horizon_color(self, x, y, face):
        """Generate horizon color with clouds and atmospheric perspective."""
        # Vertical gradient from horizon to sky
        height_factor = (y + 1) / 2  # 0 at bottom, 1 at top

        # Base sky color gradient
        horizon_r = int(20 + height_factor * 10)
        horizon_g = int(30 + height_factor * 15)
        horizon_b = int(60 + height_factor * 20)

        # Add clouds using multiple noise layers
        cloud_noise1 = np.sin(x * 4) * np.cos(y * 3) * 0.5
        cloud_noise2 = np.sin(x * 8) * np.cos(y * 6) * 0.3
        cloud_noise3 = np.sin(x * 16) * np.cos(y * 12) * 0.2

        cloud_density = (cloud_noise1 + cloud_noise2 + cloud_noise3) * (1 - height_factor * 0.7)

        if cloud_density > 0.3:
            # Cloud color
            cloud_intensity = min(1, (cloud_density - 0.3) * 2)
            cloud_r = int(cloud_intensity * 60)
            cloud_g = int(cloud_intensity * 60)
            cloud_b = int(cloud_intensity * 80)
        else:
            cloud_r = cloud_g = cloud_b = 0

        # Combine sky and clouds
        final_r = min(255, horizon_r + cloud_r)
        final_g = min(255, horizon_g + cloud_g)
        final_b = min(255, horizon_b + cloud_b)

        return [final_r, final_g, final_b]

    def setup_camera(self, player):
        """
        Set up the camera based on player position and orientation.
        
        Args:
            player: Player object with position and rotation
        """
        glLoadIdentity()
        
        # Calculate look direction based on player rotation
        yaw_rad = math.radians(player.yaw)
        pitch_rad = math.radians(player.pitch)
        
        # Calculate forward vector
        forward_x = math.cos(pitch_rad) * math.sin(yaw_rad)
        forward_y = math.sin(pitch_rad)
        forward_z = math.cos(pitch_rad) * math.cos(yaw_rad)
        
        # Player position
        px, py, pz = player.position
        
        # Look at point
        look_x = px + forward_x
        look_y = py + forward_y
        look_z = pz + forward_z
        
        # Set up camera
        gluLookAt(px, py, pz,           # Eye position
                  look_x, look_y, look_z,  # Look at point
                  0, 1, 0)              # Up vector
    
    def render_maze(self, maze):
        """
        Render the entire maze with enhanced graphics.

        Args:
            maze: Maze object to render
        """
        # Render skybox first (before fog and depth testing)
        self._render_skybox()

        # Enable fog for atmospheric effect
        self._setup_fog()

        # Enable texturing
        glEnable(GL_TEXTURE_2D)

        # Render floor with texture
        self._render_floor(maze)

        # Render walls with texture
        self._render_walls(maze)

        # Render ceiling
        self._render_ceiling(maze)

        # Render atmospheric particles
        self._render_particles()

        # Render start and end markers
        self._render_markers(maze)

        # Disable texturing
        glDisable(GL_TEXTURE_2D)

    def _render_skybox(self):
        """Render the procedural skybox."""
        # Save current matrix
        glPushMatrix()

        # Disable depth writing (skybox should be behind everything)
        glDepthMask(GL_FALSE)

        # Disable fog and lighting for skybox
        glDisable(GL_FOG)
        glDisable(GL_LIGHTING)

        # Enable texturing
        glEnable(GL_TEXTURE_2D)

        # Set full brightness for skybox
        glColor3f(1.0, 1.0, 1.0)

        # Scale skybox to be very large
        skybox_size = 40

        # Front face
        glBindTexture(GL_TEXTURE_2D, self.skybox_textures['front'])
        glBegin(GL_QUADS)
        glTexCoord2f(0, 0); glVertex3f(-skybox_size, -skybox_size, -skybox_size)
        glTexCoord2f(1, 0); glVertex3f(skybox_size, -skybox_size, -skybox_size)
        glTexCoord2f(1, 1); glVertex3f(skybox_size, skybox_size, -skybox_size)
        glTexCoord2f(0, 1); glVertex3f(-skybox_size, skybox_size, -skybox_size)
        glEnd()

        # Back face
        glBindTexture(GL_TEXTURE_2D, self.skybox_textures['back'])
        glBegin(GL_QUADS)
        glTexCoord2f(1, 0); glVertex3f(-skybox_size, -skybox_size, skybox_size)
        glTexCoord2f(1, 1); glVertex3f(-skybox_size, skybox_size, skybox_size)
        glTexCoord2f(0, 1); glVertex3f(skybox_size, skybox_size, skybox_size)
        glTexCoord2f(0, 0); glVertex3f(skybox_size, -skybox_size, skybox_size)
        glEnd()

        # Left face
        glBindTexture(GL_TEXTURE_2D, self.skybox_textures['left'])
        glBegin(GL_QUADS)
        glTexCoord2f(1, 0); glVertex3f(-skybox_size, -skybox_size, -skybox_size)
        glTexCoord2f(1, 1); glVertex3f(-skybox_size, skybox_size, -skybox_size)
        glTexCoord2f(0, 1); glVertex3f(-skybox_size, skybox_size, skybox_size)
        glTexCoord2f(0, 0); glVertex3f(-skybox_size, -skybox_size, skybox_size)
        glEnd()

        # Right face
        glBindTexture(GL_TEXTURE_2D, self.skybox_textures['right'])
        glBegin(GL_QUADS)
        glTexCoord2f(0, 0); glVertex3f(skybox_size, -skybox_size, -skybox_size)
        glTexCoord2f(1, 0); glVertex3f(skybox_size, -skybox_size, skybox_size)
        glTexCoord2f(1, 1); glVertex3f(skybox_size, skybox_size, skybox_size)
        glTexCoord2f(0, 1); glVertex3f(skybox_size, skybox_size, -skybox_size)
        glEnd()

        # Top face
        glBindTexture(GL_TEXTURE_2D, self.skybox_textures['top'])
        glBegin(GL_QUADS)
        glTexCoord2f(0, 1); glVertex3f(-skybox_size, skybox_size, -skybox_size)
        glTexCoord2f(0, 0); glVertex3f(skybox_size, skybox_size, -skybox_size)
        glTexCoord2f(1, 0); glVertex3f(skybox_size, skybox_size, skybox_size)
        glTexCoord2f(1, 1); glVertex3f(-skybox_size, skybox_size, skybox_size)
        glEnd()

        # Bottom face
        glBindTexture(GL_TEXTURE_2D, self.skybox_textures['bottom'])
        glBegin(GL_QUADS)
        glTexCoord2f(1, 1); glVertex3f(-skybox_size, -skybox_size, -skybox_size)
        glTexCoord2f(0, 1); glVertex3f(-skybox_size, -skybox_size, skybox_size)
        glTexCoord2f(0, 0); glVertex3f(skybox_size, -skybox_size, skybox_size)
        glTexCoord2f(1, 0); glVertex3f(skybox_size, -skybox_size, -skybox_size)
        glEnd()

        # Restore settings
        glDisable(GL_TEXTURE_2D)
        glDepthMask(GL_TRUE)
        glEnable(GL_FOG)
        glEnable(GL_LIGHTING)

        # Restore matrix
        glPopMatrix()

    def _setup_fog(self):
        """Set up atmospheric fog effect."""
        glEnable(GL_FOG)
        glFogi(GL_FOG_MODE, GL_LINEAR)
        glFogfv(GL_FOG_COLOR, [0.1, 0.1, 0.2, 1.0])  # Dark blue fog
        glFogf(GL_FOG_START, 8.0)
        glFogf(GL_FOG_END, 25.0)
        glHint(GL_FOG_HINT, GL_NICEST)

    def _render_floor(self, maze):
        """Render the maze floor with stone texture."""
        glBindTexture(GL_TEXTURE_2D, self.floor_texture)
        glColor3f(0.8, 0.8, 0.9)  # Slight blue tint

        glBegin(GL_QUADS)
        for z in range(maze.height):
            for x in range(maze.width):
                if maze.get_cell_type(x, z) != maze.WALL:
                    # Render floor tile with texture coordinates
                    glNormal3f(0, 1, 0)

                    glTexCoord2f(0, 0)
                    glVertex3f(x, 0, z)
                    glTexCoord2f(1, 0)
                    glVertex3f(x + 1, 0, z)
                    glTexCoord2f(1, 1)
                    glVertex3f(x + 1, 0, z + 1)
                    glTexCoord2f(0, 1)
                    glVertex3f(x, 0, z + 1)
        glEnd()
    
    def _render_walls(self, maze):
        """Render maze walls with brick texture and lighting."""
        glBindTexture(GL_TEXTURE_2D, self.wall_texture)

        for z in range(maze.height):
            for x in range(maze.width):
                if maze.get_cell_type(x, z) == maze.WALL:
                    # Vary wall color slightly based on position for depth
                    color_var = 0.1 * ((x + z) % 3) / 2
                    glColor3f(0.7 + color_var, 0.6 + color_var, 0.5 + color_var)
                    self._render_textured_cube(x, 0, z, 1, 2.5, 1)  # Taller walls

    def _render_ceiling(self, maze):
        """Render a dark ceiling for enclosed feeling."""
        glBindTexture(GL_TEXTURE_2D, self.floor_texture)
        glColor3f(0.2, 0.2, 0.3)  # Dark ceiling

        glBegin(GL_QUADS)
        for z in range(maze.height):
            for x in range(maze.width):
                if maze.get_cell_type(x, z) != maze.WALL:
                    # Render ceiling tile
                    glNormal3f(0, -1, 0)  # Normal pointing down

                    glTexCoord2f(0, 0)
                    glVertex3f(x, 2.5, z)
                    glTexCoord2f(0, 1)
                    glVertex3f(x, 2.5, z + 1)
                    glTexCoord2f(1, 1)
                    glVertex3f(x + 1, 2.5, z + 1)
                    glTexCoord2f(1, 0)
                    glVertex3f(x + 1, 2.5, z)
        glEnd()

    def _render_particles(self):
        """Render floating dust particles for atmosphere."""
        current_time = time.time() - self.start_time

        glDisable(GL_TEXTURE_2D)
        glEnable(GL_BLEND)
        glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA)
        glPointSize(2.0)

        glBegin(GL_POINTS)
        for particle in self.particles:
            # Animate particle position
            y_offset = math.sin(current_time * particle['speed'] + particle['phase']) * 0.5
            alpha = 0.3 + 0.2 * math.sin(current_time * 2 + particle['phase'])

            glColor4f(0.8, 0.8, 1.0, alpha)
            glVertex3f(particle['x'], particle['y'] + y_offset, particle['z'])
        glEnd()

        glDisable(GL_BLEND)
        glEnable(GL_TEXTURE_2D)

    def _render_cube(self, x, y, z, width, height, depth):
        """
        Render a cube at the specified position.
        
        Args:
            x, y, z: Position
            width, height, depth: Dimensions
        """
        glBegin(GL_QUADS)
        
        # Front face
        glNormal3f(0, 0, 1)
        glVertex3f(x, y, z + depth)
        glVertex3f(x + width, y, z + depth)
        glVertex3f(x + width, y + height, z + depth)
        glVertex3f(x, y + height, z + depth)
        
        # Back face
        glNormal3f(0, 0, -1)
        glVertex3f(x, y, z)
        glVertex3f(x, y + height, z)
        glVertex3f(x + width, y + height, z)
        glVertex3f(x + width, y, z)
        
        # Left face
        glNormal3f(-1, 0, 0)
        glVertex3f(x, y, z)
        glVertex3f(x, y, z + depth)
        glVertex3f(x, y + height, z + depth)
        glVertex3f(x, y + height, z)
        
        # Right face
        glNormal3f(1, 0, 0)
        glVertex3f(x + width, y, z)
        glVertex3f(x + width, y + height, z)
        glVertex3f(x + width, y + height, z + depth)
        glVertex3f(x + width, y, z + depth)
        
        # Top face
        glNormal3f(0, 1, 0)
        glVertex3f(x, y + height, z)
        glVertex3f(x, y + height, z + depth)
        glVertex3f(x + width, y + height, z + depth)
        glVertex3f(x + width, y + height, z)
        
        # Bottom face
        glNormal3f(0, -1, 0)
        glVertex3f(x, y, z)
        glVertex3f(x + width, y, z)
        glVertex3f(x + width, y, z + depth)
        glVertex3f(x, y, z + depth)
        
        glEnd()

    def _render_textured_cube(self, x, y, z, width, height, depth):
        """
        Render a textured cube at the specified position.

        Args:
            x, y, z: Position
            width, height, depth: Dimensions
        """
        glBegin(GL_QUADS)

        # Front face
        glNormal3f(0, 0, 1)
        glTexCoord2f(0, 0); glVertex3f(x, y, z + depth)
        glTexCoord2f(1, 0); glVertex3f(x + width, y, z + depth)
        glTexCoord2f(1, 1); glVertex3f(x + width, y + height, z + depth)
        glTexCoord2f(0, 1); glVertex3f(x, y + height, z + depth)

        # Back face
        glNormal3f(0, 0, -1)
        glTexCoord2f(1, 0); glVertex3f(x, y, z)
        glTexCoord2f(1, 1); glVertex3f(x, y + height, z)
        glTexCoord2f(0, 1); glVertex3f(x + width, y + height, z)
        glTexCoord2f(0, 0); glVertex3f(x + width, y, z)

        # Left face
        glNormal3f(-1, 0, 0)
        glTexCoord2f(0, 0); glVertex3f(x, y, z)
        glTexCoord2f(1, 0); glVertex3f(x, y, z + depth)
        glTexCoord2f(1, 1); glVertex3f(x, y + height, z + depth)
        glTexCoord2f(0, 1); glVertex3f(x, y + height, z)

        # Right face
        glNormal3f(1, 0, 0)
        glTexCoord2f(1, 0); glVertex3f(x + width, y, z)
        glTexCoord2f(1, 1); glVertex3f(x + width, y + height, z)
        glTexCoord2f(0, 1); glVertex3f(x + width, y + height, z + depth)
        glTexCoord2f(0, 0); glVertex3f(x + width, y, z + depth)

        # Top face
        glNormal3f(0, 1, 0)
        glTexCoord2f(0, 1); glVertex3f(x, y + height, z)
        glTexCoord2f(0, 0); glVertex3f(x, y + height, z + depth)
        glTexCoord2f(1, 0); glVertex3f(x + width, y + height, z + depth)
        glTexCoord2f(1, 1); glVertex3f(x + width, y + height, z)

        # Bottom face
        glNormal3f(0, -1, 0)
        glTexCoord2f(1, 1); glVertex3f(x, y, z)
        glTexCoord2f(0, 1); glVertex3f(x + width, y, z)
        glTexCoord2f(0, 0); glVertex3f(x + width, y, z + depth)
        glTexCoord2f(1, 0); glVertex3f(x, y, z + depth)

        glEnd()

    def _render_markers(self, maze):
        """Render start and end position markers with enhanced effects."""
        current_time = time.time() - self.start_time

        # Disable texturing for markers
        glDisable(GL_TEXTURE_2D)

        # Start marker (green with rotating glow)
        pulse = abs(math.sin(current_time * 4)) * 0.4 + 0.6
        glow_size = 0.3 + abs(math.sin(current_time * 2)) * 0.1

        # Render glowing base
        glEnable(GL_BLEND)
        glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA)
        glColor4f(0, pulse, 0, 0.5)
        start_x, start_y, start_z = maze.start_pos
        self._render_cube(start_x - glow_size, 0.05, start_z - glow_size,
                         glow_size * 2, 0.1, glow_size * 2)

        # Solid marker
        glDisable(GL_BLEND)
        glColor3f(0, pulse, 0)
        self._render_cube(start_x - 0.2, 0.1, start_z - 0.2, 0.4, 0.4, 0.4)

        # End marker (red with pulsing effect)
        end_pulse = abs(math.sin(current_time * 3)) * 0.5 + 0.5
        end_glow_size = 0.35 + abs(math.cos(current_time * 2.5)) * 0.15

        # Render glowing base
        glEnable(GL_BLEND)
        glColor4f(end_pulse, 0, 0, 0.6)
        end_x, end_y, end_z = maze.end_pos
        self._render_cube(end_x - end_glow_size, 0.05, end_z - end_glow_size,
                         end_glow_size * 2, 0.15, end_glow_size * 2)

        # Solid marker
        glDisable(GL_BLEND)
        glColor3f(end_pulse, 0, 0)
        self._render_cube(end_x - 0.25, 0.1, end_z - 0.25, 0.5, 0.5, 0.5)

        # Re-enable texturing
        glEnable(GL_TEXTURE_2D)
    
    def render_ui(self, game_state):
        """
        Render UI elements.
        
        Args:
            game_state: Current game state
        """
        # Switch to 2D rendering for UI
        glMatrixMode(GL_PROJECTION)
        glPushMatrix()
        glLoadIdentity()
        glOrtho(0, self.width, self.height, 0, -1, 1)
        
        glMatrixMode(GL_MODELVIEW)
        glPushMatrix()
        glLoadIdentity()
        
        # Disable depth testing for UI
        glDisable(GL_DEPTH_TEST)
        
        # Render crosshair
        self._render_crosshair()
        
        # Render win message if game is won
        if game_state.is_won():
            self._render_win_message()
        
        # Re-enable depth testing
        glEnable(GL_DEPTH_TEST)
        
        # Restore matrices
        glPopMatrix()
        glMatrixMode(GL_PROJECTION)
        glPopMatrix()
        glMatrixMode(GL_MODELVIEW)
    
    def _render_crosshair(self):
        """Render an enhanced crosshair in the center of the screen."""
        center_x = self.width // 2
        center_y = self.height // 2

        # Outer crosshair (darker)
        glColor4f(0, 0, 0, 0.8)
        glLineWidth(3)
        size = 12

        glBegin(GL_LINES)
        # Horizontal line
        glVertex2f(center_x - size, center_y)
        glVertex2f(center_x + size, center_y)
        # Vertical line
        glVertex2f(center_x, center_y - size)
        glVertex2f(center_x, center_y + size)
        glEnd()

        # Inner crosshair (bright)
        glColor3f(0.8, 1, 0.8)  # Light green
        glLineWidth(1)
        size = 8

        glBegin(GL_LINES)
        # Horizontal line
        glVertex2f(center_x - size, center_y)
        glVertex2f(center_x + size, center_y)
        # Vertical line
        glVertex2f(center_x, center_y - size)
        glVertex2f(center_x, center_y + size)
        glEnd()

        # Center dot
        glPointSize(3)
        glBegin(GL_POINTS)
        glVertex2f(center_x, center_y)
        glEnd()
    
    def _render_win_message(self):
        """Render enhanced win message overlay with effects."""
        current_time = time.time() - self.start_time

        # Animated semi-transparent overlay
        alpha = 0.6 + 0.1 * math.sin(current_time * 3)
        glColor4f(0, 0.1, 0, alpha)  # Dark green tint
        glEnable(GL_BLEND)
        glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA)

        glBegin(GL_QUADS)
        glVertex2f(0, 0)
        glVertex2f(self.width, 0)
        glVertex2f(self.width, self.height)
        glVertex2f(0, self.height)
        glEnd()

        # Pulsing border effect
        border_alpha = abs(math.sin(current_time * 4)) * 0.8
        glColor4f(0, 1, 0, border_alpha)
        glLineWidth(5)

        glBegin(GL_LINE_LOOP)
        margin = 20
        glVertex2f(margin, margin)
        glVertex2f(self.width - margin, margin)
        glVertex2f(self.width - margin, self.height - margin)
        glVertex2f(margin, self.height - margin)
        glEnd()

        glDisable(GL_BLEND)
