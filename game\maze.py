"""
Maze generation and management system.
Uses recursive backtracking algorithm to generate procedural mazes.
"""

import random
import numpy as np

class Maze:
    """Represents a 3D maze with walls, floors, start and end positions."""
    
    # Cell types
    WALL = 1
    FLOOR = 0
    START = 2
    END = 3
    
    def __init__(self, width, height):
        """
        Initialize maze with given dimensions.
        
        Args:
            width: Width of the maze (should be odd)
            height: Height of the maze (should be odd)
        """
        # Ensure dimensions are odd for proper maze generation
        self.width = width if width % 2 == 1 else width + 1
        self.height = height if height % 2 == 1 else height + 1
        
        # Initialize maze grid (1 = wall, 0 = floor)
        self.grid = np.ones((self.height, self.width), dtype=int)
        
        # Generate the maze
        self._generate_maze()
        
        # Set start and end positions
        self.start_pos = (1.5, 0.5, 1.5)  # Start at top-left corner
        self.end_pos = (self.width - 1.5, 0.5, self.height - 1.5)  # End at bottom-right
        
        # Mark start and end in grid
        self.grid[1, 1] = self.START
        self.grid[self.height - 2, self.width - 2] = self.END
    
    def _generate_maze(self):
        """Generate maze using recursive backtracking algorithm."""
        # Start from position (1, 1)
        start_x, start_y = 1, 1
        self.grid[start_y, start_x] = self.FLOOR
        
        # Stack for backtracking
        stack = [(start_x, start_y)]
        
        # Directions: right, down, left, up
        directions = [(2, 0), (0, 2), (-2, 0), (0, -2)]
        
        while stack:
            current_x, current_y = stack[-1]
            
            # Get unvisited neighbors
            neighbors = []
            for dx, dy in directions:
                nx, ny = current_x + dx, current_y + dy
                
                # Check if neighbor is within bounds and is a wall (unvisited)
                if (0 < nx < self.width - 1 and 
                    0 < ny < self.height - 1 and 
                    self.grid[ny, nx] == self.WALL):
                    neighbors.append((nx, ny, dx, dy))
            
            if neighbors:
                # Choose random neighbor
                nx, ny, dx, dy = random.choice(neighbors)
                
                # Remove wall between current cell and chosen neighbor
                wall_x = current_x + dx // 2
                wall_y = current_y + dy // 2
                
                self.grid[ny, nx] = self.FLOOR
                self.grid[wall_y, wall_x] = self.FLOOR
                
                # Add neighbor to stack
                stack.append((nx, ny))
            else:
                # Backtrack
                stack.pop()
    
    def is_wall(self, x, z):
        """
        Check if position (x, z) is a wall.
        
        Args:
            x: X coordinate in world space
            z: Z coordinate in world space
            
        Returns:
            True if position is a wall, False otherwise
        """
        # Convert world coordinates to grid coordinates
        grid_x = int(x)
        grid_z = int(z)
        
        # Check bounds
        if (grid_x < 0 or grid_x >= self.width or 
            grid_z < 0 or grid_z >= self.height):
            return True  # Out of bounds is considered a wall
        
        return self.grid[grid_z, grid_x] == self.WALL
    
    def is_at_exit(self, position):
        """
        Check if player is at the exit position.
        
        Args:
            position: Player position (x, y, z)
            
        Returns:
            True if player is at exit, False otherwise
        """
        x, y, z = position
        exit_x, exit_y, exit_z = self.end_pos
        
        # Check if player is within exit area (1x1 square)
        return (abs(x - exit_x) < 0.5 and abs(z - exit_z) < 0.5)
    
    def get_cell_type(self, x, z):
        """
        Get the type of cell at grid position (x, z).
        
        Args:
            x: Grid X coordinate
            z: Grid Z coordinate
            
        Returns:
            Cell type (WALL, FLOOR, START, or END)
        """
        if (x < 0 or x >= self.width or z < 0 or z >= self.height):
            return self.WALL
        
        return self.grid[z, x]
