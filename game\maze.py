"""
Arena generation and management system for shooter game.
Creates tactical environments with cover points, open areas, and strategic layouts.
"""

import random
import numpy as np

class Maze:
    """Represents a 3D tactical arena with walls, floors, cover points, and spawn areas."""

    # Cell types
    WALL = 1
    FLOOR = 0
    START = 2
    END = 3
    COVER = 4      # Low cover walls
    PILLAR = 5     # Tall pillars for cover
    SPAWN_AREA = 6 # Enemy spawn points
    
    def __init__(self, width, height):
        """
        Initialize tactical arena with given dimensions.

        Args:
            width: Width of the arena
            height: Height of the arena
        """
        self.width = width
        self.height = height

        # Initialize arena grid (1 = wall, 0 = floor)
        self.grid = np.zeros((self.height, self.width), dtype=int)

        # Initialize lists BEFORE arena generation
        self.enemy_spawns = []
        self.cover_positions = []

        # Generate the tactical arena
        self._generate_arena()

        # Set player spawn position (center-ish)
        self.start_pos = (self.width // 2 + 0.5, 0.5, self.height // 2 + 0.5)

        # No single "end" position - this is now a combat arena
        self.end_pos = None

        # Mark player spawn in grid
        center_x, center_z = self.width // 2, self.height // 2
        self.grid[center_z, center_x] = self.START
    
    def _generate_arena(self):
        """Generate tactical arena with strategic layout."""
        # Create perimeter walls
        self._create_perimeter_walls()

        # Create main open areas
        self._create_open_areas()

        # Add strategic cover points
        self._add_cover_elements()

        # Add pillars and obstacles
        self._add_pillars()

        # Create enemy spawn areas
        self._create_spawn_areas()

        # Add some connecting corridors
        self._add_corridors()

    def _create_perimeter_walls(self):
        """Create walls around the arena perimeter."""
        # Top and bottom walls
        self.grid[0, :] = self.WALL
        self.grid[self.height - 1, :] = self.WALL

        # Left and right walls
        self.grid[:, 0] = self.WALL
        self.grid[:, self.width - 1] = self.WALL

    def _create_open_areas(self):
        """Create large open areas for combat."""
        # Central arena area
        center_x, center_z = self.width // 2, self.height // 2

        # Large central open area
        for z in range(center_z - 4, center_z + 5):
            for x in range(center_x - 6, center_x + 7):
                if 0 < x < self.width - 1 and 0 < z < self.height - 1:
                    self.grid[z, x] = self.FLOOR

        # Corner areas for flanking
        corner_size = 3
        corners = [
            (corner_size, corner_size),  # Top-left
            (self.width - corner_size - 1, corner_size),  # Top-right
            (corner_size, self.height - corner_size - 1),  # Bottom-left
            (self.width - corner_size - 1, self.height - corner_size - 1)  # Bottom-right
        ]

        for cx, cz in corners:
            for z in range(cz - 2, cz + 3):
                for x in range(cx - 2, cx + 3):
                    if 0 < x < self.width - 1 and 0 < z < self.height - 1:
                        self.grid[z, x] = self.FLOOR

    def _add_cover_elements(self):
        """Add low cover walls for tactical gameplay."""
        # Add some scattered low cover
        cover_positions = [
            (self.width // 4, self.height // 3),
            (3 * self.width // 4, self.height // 3),
            (self.width // 4, 2 * self.height // 3),
            (3 * self.width // 4, 2 * self.height // 3),
            (self.width // 2 - 3, self.height // 2),
            (self.width // 2 + 3, self.height // 2),
        ]

        for cx, cz in cover_positions:
            if 0 < cx < self.width - 1 and 0 < cz < self.height - 1:
                # Create L-shaped cover
                self.grid[cz, cx] = self.COVER
                self.grid[cz, cx + 1] = self.COVER
                self.grid[cz + 1, cx] = self.COVER
                self.cover_positions.append((cx + 0.5, 0.5, cz + 0.5))

    def _add_pillars(self):
        """Add tall pillars for vertical cover."""
        pillar_positions = [
            (self.width // 3, self.height // 4),
            (2 * self.width // 3, self.height // 4),
            (self.width // 3, 3 * self.height // 4),
            (2 * self.width // 3, 3 * self.height // 4),
        ]

        for px, pz in pillar_positions:
            if 0 < px < self.width - 1 and 0 < pz < self.height - 1:
                self.grid[pz, px] = self.PILLAR

    def _create_spawn_areas(self):
        """Create enemy spawn areas around the perimeter."""
        # Spawn points along the walls
        spawn_points = [
            (2, 2), (self.width - 3, 2),  # Top corners
            (2, self.height - 3), (self.width - 3, self.height - 3),  # Bottom corners
            (self.width // 2, 2),  # Top center
            (self.width // 2, self.height - 3),  # Bottom center
            (2, self.height // 2),  # Left center
            (self.width - 3, self.height // 2),  # Right center
        ]

        for sx, sz in spawn_points:
            if 0 < sx < self.width - 1 and 0 < sz < self.height - 1:
                self.grid[sz, sx] = self.SPAWN_AREA
                self.enemy_spawns.append((sx + 0.5, 0.5, sz + 0.5))

    def _add_corridors(self):
        """Add connecting corridors between areas."""
        center_x, center_z = self.width // 2, self.height // 2

        # Horizontal corridors
        for x in range(3, self.width - 3):
            self.grid[center_z, x] = self.FLOOR

        # Vertical corridors
        for z in range(3, self.height - 3):
            self.grid[z, center_x] = self.FLOOR

        # Diagonal connections
        for i in range(-2, 3):
            if (0 < center_x + i < self.width - 1 and
                0 < center_z + i < self.height - 1):
                self.grid[center_z + i, center_x + i] = self.FLOOR
            if (0 < center_x - i < self.width - 1 and
                0 < center_z + i < self.height - 1):
                self.grid[center_z + i, center_x - i] = self.FLOOR
    
    def is_wall(self, x, z):
        """
        Check if position (x, z) is a solid obstacle.

        Args:
            x: X coordinate in world space
            z: Z coordinate in world space

        Returns:
            True if position blocks movement, False otherwise
        """
        # Convert world coordinates to grid coordinates
        grid_x = int(x)
        grid_z = int(z)

        # Check bounds
        if (grid_x < 0 or grid_x >= self.width or
            grid_z < 0 or grid_z >= self.height):
            return True  # Out of bounds is considered a wall

        cell_type = self.grid[grid_z, grid_x]
        # Walls, cover, and pillars block movement
        return cell_type in [self.WALL, self.COVER, self.PILLAR]
    
    def get_enemy_spawns(self):
        """
        Get all enemy spawn positions.

        Returns:
            List of enemy spawn positions
        """
        return self.enemy_spawns.copy()

    def get_cover_positions(self):
        """
        Get all cover positions for tactical AI.

        Returns:
            List of cover positions
        """
        return self.cover_positions.copy()

    def is_cover_position(self, x, z):
        """
        Check if position provides cover.

        Args:
            x: X coordinate
            z: Z coordinate

        Returns:
            True if position has cover nearby
        """
        grid_x, grid_z = int(x), int(z)

        # Check surrounding cells for cover
        for dx in [-1, 0, 1]:
            for dz in [-1, 0, 1]:
                check_x, check_z = grid_x + dx, grid_z + dz
                if (0 <= check_x < self.width and 0 <= check_z < self.height):
                    if self.grid[check_z, check_x] in [self.COVER, self.PILLAR]:
                        return True
        return False
    
    def get_cell_type(self, x, z):
        """
        Get the type of cell at grid position (x, z).
        
        Args:
            x: Grid X coordinate
            z: Grid Z coordinate
            
        Returns:
            Cell type (WALL, FLOOR, START, or END)
        """
        if (x < 0 or x >= self.width or z < 0 or z >= self.height):
            return self.WALL
        
        return self.grid[z, x]
